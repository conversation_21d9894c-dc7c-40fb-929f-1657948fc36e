<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>价格计算器测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-case {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-case h3 {
            color: #333;
            margin-top: 0;
        }
        .test-input {
            margin: 10px 0;
        }
        .test-input label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        .expected {
            background-color: #e8f5e8;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .button {
            background-color: #667eea;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .button:hover {
            background-color: #5a6fd8;
        }
    </style>
</head>
<body>
    <h1>价格计算器测试用例</h1>
    
    <div class="test-case">
        <h3>测试用例 1: 售后卡 - 低成本价</h3>
        <div class="test-input">
            <label>原始成本价:</label> ¥5.00
        </div>
        <div class="test-input">
            <label>版式规格:</label> 90-54以下 (¥8)
        </div>
        <div class="test-input">
            <label>版式数量:</label> 2
        </div>
        <div class="expected">
            <strong>预期结果:</strong><br>
            倍率: 2.2<br>
            计算后成本价: ¥5.00 × 2.2 = ¥11.00<br>
            实际成本价: ¥15.00 (最低限制)<br>
            版式费: ¥8 × 2 = ¥16.00<br>
            最终价格: ¥15.00 + ¥16.00 = ¥31.00
        </div>
        <button class="button" onclick="openCalculator('afterSale', 5, 8, 2)">在计算器中测试</button>
    </div>

    <div class="test-case">
        <h3>测试用例 2: 售后卡 - 中等成本价</h3>
        <div class="test-input">
            <label>原始成本价:</label> ¥25.00
        </div>
        <div class="test-input">
            <label>版式规格:</label> 140-100以上285-210以下 (¥20)
        </div>
        <div class="test-input">
            <label>版式数量:</label> 3
        </div>
        <div class="expected">
            <strong>预期结果:</strong><br>
            倍率: 1.7<br>
            计算后成本价: ¥25.00 × 1.7 = ¥42.50<br>
            实际成本价: ¥42.50<br>
            版式费: ¥20 × 3 = ¥60.00<br>
            最终价格: ¥42.50 + ¥60.00 = ¥102.50
        </div>
        <button class="button" onclick="openCalculator('afterSale', 25, 20, 3)">在计算器中测试</button>
    </div>

    <div class="test-case">
        <h3>测试用例 3: PVC卡亚克力 - 高成本价</h3>
        <div class="test-input">
            <label>原始成本价:</label> ¥150.00
        </div>
        <div class="test-input">
            <label>版式规格:</label> 420-285以上570-420以下 (¥35)
        </div>
        <div class="test-input">
            <label>版式数量:</label> 1
        </div>
        <div class="expected">
            <strong>预期结果:</strong><br>
            倍率: 1.5<br>
            计算后成本价: ¥150.00 × 1.5 = ¥225.00<br>
            实际成本价: ¥225.00<br>
            版式费: ¥35 × 1 = ¥35.00<br>
            最终价格: ¥225.00 + ¥35.00 = ¥260.00
        </div>
        <button class="button" onclick="openCalculator('pvcAcrylic', 150, 35, 1)">在计算器中测试</button>
    </div>

    <div class="test-case">
        <h3>功能测试</h3>
        <p>请测试以下功能：</p>
        <ul>
            <li>Tab切换是否正常工作</li>
            <li>实时计算功能</li>
            <li>输入验证（负数、空值等）</li>
            <li>移动端响应式设计</li>
            <li>计算明细显示</li>
        </ul>
        <button class="button" onclick="window.open('index.html', '_blank')">打开计算器</button>
    </div>

    <script>
        function openCalculator(category, baseValue, layoutPrice, layoutQuantity) {
            const url = `index.html#${category}`;
            const newWindow = window.open(url, '_blank');
            
            // 等待页面加载后填充数据
            setTimeout(() => {
                if (newWindow && !newWindow.closed) {
                    try {
                        // 切换到对应的tab
                        const tab = newWindow.document.querySelector(`[data-tab="${category}"]`);
                        if (tab) tab.click();
                        
                        // 填充数据
                        setTimeout(() => {
                            const baseInput = newWindow.document.getElementById(`${category}-baseValue`);
                            const layoutSelect = newWindow.document.getElementById(`${category}-layoutPrice`);
                            const quantityInput = newWindow.document.getElementById(`${category}-layoutQuantity`);
                            
                            if (baseInput) baseInput.value = baseValue;
                            if (layoutSelect) layoutSelect.value = layoutPrice;
                            if (quantityInput) quantityInput.value = layoutQuantity;
                            
                            // 触发计算
                            if (baseInput) {
                                const event = new Event('input', { bubbles: true });
                                baseInput.dispatchEvent(event);
                            }
                        }, 500);
                    } catch (e) {
                        console.log('无法自动填充数据，请手动输入');
                    }
                }
            }, 1000);
        }
    </script>
</body>
</html>
