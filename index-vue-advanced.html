<!doctype html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>价格计算器 - Vue高级版本</title>
  <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      background-attachment: fixed;
      min-height: 100vh;
      padding: 20px;
      position: relative;
    }

    body::before {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.08)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
      pointer-events: none;
      z-index: 0;
    }

    .container {
      max-width: 1000px;
      margin: 0 auto;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: 20px;
      box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.1);
      overflow: hidden;
      position: relative;
      z-index: 1;
    }

    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 40px 30px 20px;
      text-align: center;
      position: relative;
      overflow: hidden;
    }

    .header::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
      animation: float 6s ease-in-out infinite;
    }

    @keyframes float {

      0%,
      100% {
        transform: translate(-50%, -50%) rotate(0deg);
      }

      50% {
        transform: translate(-50%, -50%) rotate(180deg);
      }
    }

    .header h1 {
      font-size: 3em;
      margin-bottom: 15px;
      font-weight: 200;
      letter-spacing: 2px;
      position: relative;
      z-index: 2;
    }

    .header p {
      font-size: 1.2em;
      opacity: 0.9;
      position: relative;
      z-index: 2;
      font-weight: 300;
    }

    .admin-panel {
      background: rgba(255, 255, 255, 0.1);
      padding: 15px;
      margin-top: 20px;
      border-radius: 10px;
      position: relative;
      z-index: 2;
    }

    .admin-toggle {
      background: rgba(255, 255, 255, 0.2);
      border: none;
      color: white;
      padding: 8px 16px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 0.9em;
      margin-bottom: 10px;
    }

    .admin-controls {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 10px;
      margin-top: 10px;
    }

    .admin-controls input,
    .admin-controls select {
      padding: 8px;
      border: none;
      border-radius: 4px;
      background: rgba(255, 255, 255, 0.9);
      font-size: 0.9em;
    }

    .tabs {
      display: flex;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 15px;
      padding: 5px;
      margin-top: 20px;
      position: relative;
      z-index: 2;
      overflow-x: auto;
      flex-wrap: wrap;
      gap: 5px;
    }

    .tab {
      flex: 1;
      padding: 12px 16px;
      text-align: center;
      background: transparent;
      border: none;
      color: rgba(255, 255, 255, 0.7);
      cursor: pointer;
      border-radius: 10px;
      transition: all 0.3s ease;
      font-size: 0.9em;
      font-weight: 500;
      white-space: nowrap;
      min-width: 120px;
      position: relative;
    }

    .tab:hover {
      color: rgba(255, 255, 255, 0.9);
      background: rgba(255, 255, 255, 0.1);
    }

    .tab.active {
      background: rgba(255, 255, 255, 0.2);
      color: white;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .tab-badge {
      position: absolute;
      top: -5px;
      right: -5px;
      background: #ff4757;
      color: white;
      border-radius: 50%;
      width: 20px;
      height: 20px;
      font-size: 0.7em;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .content {
      padding: 40px;
      display: grid;
      grid-template-columns: 1fr 300px;
      gap: 30px;
    }

    .main-form {
      animation: fadeIn 0.3s ease;
    }

    .sidebar {
      background: rgba(102, 126, 234, 0.05);
      border-radius: 15px;
      padding: 20px;
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(10px);
      }

      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .form-group {
      margin-bottom: 25px;
    }

    .form-group label {
      display: block;
      margin-bottom: 8px;
      font-weight: 600;
      color: #333;
      font-size: 1.1em;
    }

    .form-group input,
    .form-group select {
      width: 100%;
      padding: 15px 20px;
      border: 2px solid rgba(102, 126, 234, 0.1);
      border-radius: 12px;
      font-size: 1.1em;
      background: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(10px);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .form-group input:focus,
    .form-group select:focus {
      outline: none;
      border-color: #667eea;
      background: rgba(255, 255, 255, 1);
      box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1), 0 8px 25px rgba(102, 126, 234, 0.15);
      transform: translateY(-2px);
    }

    .calculate-btn {
      width: 100%;
      padding: 18px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      border-radius: 12px;
      font-size: 1.3em;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      margin-top: 30px;
      position: relative;
      overflow: hidden;
    }

    .calculate-btn:hover {
      transform: translateY(-3px);
      box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
    }

    .result {
      margin-top: 40px;
      animation: slideIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    }

    @keyframes slideIn {
      from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
      }

      to {
        opacity: 1;
        transform: translateY(0) scale(1);
      }
    }

    .final-price-card {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 20px;
      padding: 40px;
      text-align: center;
      margin-bottom: 30px;
      position: relative;
      overflow: hidden;
      box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
    }

    .final-price {
      font-size: 4em;
      font-weight: 300;
      color: white;
      margin-bottom: 10px;
      position: relative;
      z-index: 2;
      text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    }

    .price-label {
      font-size: 1.2em;
      color: rgba(255, 255, 255, 0.9);
      font-weight: 300;
      position: relative;
      z-index: 2;
    }

    .calculation-summary {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: 16px;
      padding: 25px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .summary-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid rgba(102, 126, 234, 0.1);
      font-size: 1.1em;
    }

    .summary-item:last-child {
      border-bottom: 2px solid #667eea;
      font-weight: 600;
      color: #667eea;
      margin-top: 10px;
      padding-top: 20px;
    }

    .error {
      color: #e74c3c;
      font-size: 0.9em;
      margin-top: 5px;
    }

    .input-group {
      display: flex;
      gap: 15px;
    }

    .input-group .form-group {
      flex: 1;
    }

    .sidebar h3 {
      color: #333;
      margin-bottom: 15px;
      font-size: 1.2em;
    }

    .history-item {
      background: white;
      padding: 15px;
      border-radius: 8px;
      margin-bottom: 10px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      cursor: pointer;
      transition: transform 0.2s ease;
    }

    .history-item:hover {
      transform: translateY(-2px);
    }

    .stats-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 10px;
      margin-top: 20px;
    }

    .stat-card {
      background: white;
      padding: 15px;
      border-radius: 8px;
      text-align: center;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .stat-value {
      font-size: 1.5em;
      font-weight: bold;
      color: #667eea;
    }

    .stat-label {
      font-size: 0.9em;
      color: #666;
      margin-top: 5px;
    }

    @media (max-width: 768px) {
      .content {
        grid-template-columns: 1fr;
      }

      .input-group {
        flex-direction: column;
      }

      .tabs {
        flex-direction: column;
      }

      .tab {
        min-width: auto;
      }
    }
  </style>
</head>

<body>
  <div id="app">
    <div class="container">
      <div class="header">
        <h1>价格计算器</h1>
        <p>Vue 高级动态渲染版本</p>

        <!-- 管理员面板 -->
        <div class="admin-panel" v-if="showAdminPanel">
          <button class="admin-toggle" @click="toggleAdmin">
            {{ adminMode ? '退出管理' : '管理模式' }}
          </button>

          <div v-if="adminMode" class="admin-controls">
            <input v-model="newCategoryName" placeholder="新品类名称">
            <button class="admin-toggle" @click="addCategory">添加品类</button>
            <select v-model="selectedCategoryToEdit">
              <option value="">选择要编辑的品类</option>
              <option v-for="(config, key) in categoryConfigs" :key="key" :value="key">
                {{ config.name }}
              </option>
            </select>
            <button class="admin-toggle" @click="editCategory" :disabled="!selectedCategoryToEdit">
              编辑品类
            </button>
          </div>
        </div>

        <div class="tabs">
          <button v-for="(config, key) in categoryConfigs" :key="key" class="tab"
            :class="{ active: currentCategory === key }" @click="switchTab(key)">
            {{ config.name }}
            <span v-if="getCategoryUsageCount(key) > 0" class="tab-badge">
              {{ getCategoryUsageCount(key) }}
            </span>
          </button>
        </div>
      </div>

      <div class="content">
        <div class="main-form">
          <form @submit.prevent="calculatePrice">
            <div class="form-group">
              <label>原始成本价:</label>
              <input type="number" v-model.number="formData.baseValue" min="0.01" step="0.01" required
                placeholder="请输入原始成本价" @input="autoCalculate">
              <div class="error" v-if="errors.baseValue">{{ errors.baseValue }}</div>
            </div>

            <div class="input-group">
              <div class="form-group">
                <label>版式单价:</label>
                <select v-model.number="formData.layoutPrice" required @change="autoCalculate">
                  <option value="">请选择版式规格</option>
                  <option v-for="option in currentConfig.layoutOptions" :key="option.value" :value="option.value">
                    {{ option.label }}
                  </option>
                </select>
              </div>

              <div class="form-group">
                <label>版式数量:</label>
                <input type="number" v-model.number="formData.layoutQuantity" step="1" min="1" required
                  placeholder="版式数量" @input="autoCalculate">
                <div class="error" v-if="errors.layoutQuantity">{{ errors.layoutQuantity }}</div>
              </div>
            </div>

            <button type="submit" class="calculate-btn">计算价格</button>
          </form>

          <div class="result" v-if="result.show">
            <div class="final-price-card">
              <div class="final-price">¥{{ result.finalPrice.toFixed(2) }}</div>
              <div class="price-label">最终价格</div>
            </div>

            <div class="calculation-summary">
              <h3>💡 计算明细</h3>
              <div class="summary-item">
                <span class="summary-label">品类</span>
                <span class="summary-value">{{ currentConfig.name }}</span>
              </div>
              <div class="summary-item">
                <span class="summary-label">原始成本价</span>
                <span class="summary-value">¥{{ formData.baseValue }}</span>
              </div>
              <div class="summary-item">
                <span class="summary-label">倍率</span>
                <span class="summary-value">×{{ result.multiplier }}</span>
              </div>
              <div class="summary-item">
                <span class="summary-label">实际成本价</span>
                <span class="summary-value">
                  ¥{{ result.costPrice.toFixed(2) }}
                  <span v-if="result.rawCostPrice < currentConfig.minCostPrice">
                    (最低¥{{ currentConfig.minCostPrice }})
                  </span>
                </span>
              </div>
              <div class="summary-item">
                <span class="summary-label">版式费</span>
                <span class="summary-value">
                  ¥{{ formData.layoutPrice }} × {{ formData.layoutQuantity }} = ¥{{ result.layoutFee.toFixed(2) }}
                </span>
              </div>
              <div class="summary-item">
                <span class="summary-label">最终价格</span>
                <span class="summary-value">¥{{ result.finalPrice.toFixed(2) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 侧边栏 -->
        <div class="sidebar">
          <h3>📊 统计信息</h3>
          <div class="stats-grid">
            <div class="stat-card">
              <div class="stat-value">{{ totalCalculations }}</div>
              <div class="stat-label">总计算次数</div>
            </div>
            <div class="stat-card">
              <div class="stat-value">{{ Object.keys(categoryConfigs).length }}</div>
              <div class="stat-label">品类数量</div>
            </div>
            <div class="stat-card">
              <div class="stat-value">¥{{ averagePrice.toFixed(0) }}</div>
              <div class="stat-label">平均价格</div>
            </div>
            <div class="stat-card">
              <div class="stat-value">{{ mostUsedCategory }}</div>
              <div class="stat-label">热门品类</div>
            </div>
          </div>

          <h3 style="margin-top: 30px;">📝 计算历史</h3>
          <div v-if="calculationHistory.length === 0"
            style="color: #666; font-style: italic; text-align: center; padding: 20px;">
            暂无计算记录
          </div>
          <div v-for="(item, index) in calculationHistory.slice(-5)" :key="index" class="history-item"
            @click="loadHistoryItem(item)">
            <div style="font-weight: bold; color: #667eea;">{{ item.categoryName }}</div>
            <div style="font-size: 0.9em; color: #666;">¥{{ item.baseValue }} → ¥{{ item.finalPrice.toFixed(2) }}</div>
            <div style="font-size: 0.8em; color: #999;">{{ formatTime(item.timestamp) }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    const { createApp } = Vue;

    createApp({
      data () {
        return {
          currentCategory: 'afterSale',
          formData: {
            baseValue: '',
            layoutPrice: '',
            layoutQuantity: ''
          },
          errors: {},
          result: {
            show: false,
            finalPrice: 0,
            costPrice: 0,
            rawCostPrice: 0,
            layoutFee: 0,
            multiplier: 0
          },
          calculationHistory: [],
          showAdminPanel: true,
          adminMode: false,
          newCategoryName: '',
          selectedCategoryToEdit: '',
          categoryConfigs: {
            afterSale: {
              name: '售后卡',
              multiplierRanges: [
                { min: 0, max: 10.1, multiplier: 2.2 },
                { min: 10.1, max: 20, multiplier: 1.8 },
                { min: 20, max: 30, multiplier: 1.7 },
                { min: 30, max: 40, multiplier: 1.6 },
                { min: 40, max: 120, multiplier: 1.55 },
                { min: 120, max: 200, multiplier: 1.5 },
                { min: 200, max: 400, multiplier: 1.45 },
                { min: 400, max: 700, multiplier: 1.4 },
                { min: 700, max: 1000, multiplier: 1.35 },
                { min: 1000, max: 2000, multiplier: 1.3 },
                { min: 2000, max: 10000, multiplier: 1.25 },
                { min: 10000, max: Infinity, multiplier: 1.2 }
              ],
              minCostPrice: 15,
              layoutOptions: [
                { value: 8, label: '90-54以下 - ¥8' },
                { value: 13, label: '90-54以上140-100以下 - ¥13' },
                { value: 20, label: '140-100以上285-210以下 - ¥20' },
                { value: 25, label: '285-210以上420-285以下 - ¥25' },
                { value: 35, label: '420-285以上570-420以下 - ¥35' },
                { value: 50, label: '570-420以上 - ¥50' }
              ],
              minLayoutFee: 8
            },
            pvcAcrylic: {
              name: 'PVC卡亚克力',
              multiplierRanges: [
                { min: 0, max: 10.1, multiplier: 2.0 },
                { min: 10.1, max: 20, multiplier: 1.7 },
                { min: 20, max: 30, multiplier: 1.6 },
                { min: 30, max: 40, multiplier: 1.5 },
                { min: 40, max: 120, multiplier: 1.45 },
                { min: 120, max: 200, multiplier: 1.4 },
                { min: 200, max: 400, multiplier: 1.35 },
                { min: 400, max: 700, multiplier: 1.3 },
                { min: 700, max: 1000, multiplier: 1.25 },
                { min: 1000, max: 2000, multiplier: 1.2 },
                { min: 2000, max: 10000, multiplier: 1.15 },
                { min: 10000, max: Infinity, multiplier: 1.1 }
              ],
              minCostPrice: 20,
              layoutOptions: [
                { value: 10, label: '小尺寸 - ¥10' },
                { value: 15, label: '中尺寸 - ¥15' },
                { value: 25, label: '大尺寸 - ¥25' },
                { value: 40, label: '特大尺寸 - ¥40' }
              ],
              minLayoutFee: 10
            },
            businessCard: {
              name: '名片代金券单页工艺纸',
              multiplierRanges: [
                { min: 0, max: 10.1, multiplier: 1.8 },
                { min: 10.1, max: 20, multiplier: 1.6 },
                { min: 20, max: 30, multiplier: 1.5 },
                { min: 30, max: 40, multiplier: 1.4 },
                { min: 40, max: 120, multiplier: 1.35 },
                { min: 120, max: 200, multiplier: 1.3 },
                { min: 200, max: 400, multiplier: 1.25 },
                { min: 400, max: 700, multiplier: 1.2 },
                { min: 700, max: 1000, multiplier: 1.15 },
                { min: 1000, max: 2000, multiplier: 1.1 },
                { min: 2000, max: 10000, multiplier: 1.05 },
                { min: 10000, max: Infinity, multiplier: 1.0 }
              ],
              minCostPrice: 5,
              layoutOptions: [
                { value: 3, label: '标准名片 - ¥3' },
                { value: 5, label: '高档名片 - ¥5' },
                { value: 8, label: '特殊工艺 - ¥8' },
                { value: 12, label: '豪华版 - ¥12' }
              ],
              minLayoutFee: 3
            },
            portrait: {
              name: '人像证',
              multiplierRanges: [
                { min: 0, max: 10.1, multiplier: 2.5 },
                { min: 10.1, max: 20, multiplier: 2.0 },
                { min: 20, max: 30, multiplier: 1.9 },
                { min: 30, max: 40, multiplier: 1.8 },
                { min: 40, max: 120, multiplier: 1.7 },
                { min: 120, max: 200, multiplier: 1.6 },
                { min: 200, max: 400, multiplier: 1.5 },
                { min: 400, max: 700, multiplier: 1.4 },
                { min: 700, max: 1000, multiplier: 1.3 },
                { min: 1000, max: 2000, multiplier: 1.2 },
                { min: 2000, max: 10000, multiplier: 1.1 },
                { min: 10000, max: Infinity, multiplier: 1.0 }
              ],
              minCostPrice: 25,
              layoutOptions: [
                { value: 15, label: '标准证件照 - ¥15' },
                { value: 25, label: '高清证件照 - ¥25' },
                { value: 35, label: '专业证件照 - ¥35' },
                { value: 50, label: '精修证件照 - ¥50' }
              ],
              minLayoutFee: 15
            }
          }
        }
      },
      computed: {
        currentConfig () {
          return this.categoryConfigs[this.currentCategory];
        },
        totalCalculations () {
          return this.calculationHistory.length;
        },
        averagePrice () {
          if (this.calculationHistory.length === 0) return 0;
          const total = this.calculationHistory.reduce((sum, item) => sum + item.finalPrice, 0);
          return total / this.calculationHistory.length;
        },
        mostUsedCategory () {
          if (this.calculationHistory.length === 0) return '暂无';
          const counts = {};
          this.calculationHistory.forEach(item => {
            counts[item.categoryName] = (counts[item.categoryName] || 0) + 1;
          });
          const mostUsed = Object.keys(counts).reduce((a, b) => counts[a] > counts[b] ? a : b);
          return mostUsed;
        }
      },
      methods: {
        switchTab (categoryKey) {
          this.currentCategory = categoryKey;
          this.resetForm();
          this.result.show = false;
        },
        resetForm () {
          this.formData = {
            baseValue: '',
            layoutPrice: '',
            layoutQuantity: ''
          };
          this.errors = {};
        },
        getMultiplier (value) {
          const config = this.currentConfig;
          for (let range of config.multiplierRanges) {
            if (value >= range.min && value < range.max) {
              return range.multiplier;
            }
          }
          return 1.2;
        },
        validateForm () {
          this.errors = {};
          let isValid = true;

          if (!this.formData.baseValue || this.formData.baseValue <= 0) {
            this.errors.baseValue = '请输入有效的正数';
            isValid = false;
          }

          if (!this.formData.layoutPrice) {
            alert('请选择版式规格');
            isValid = false;
          }

          if (!this.formData.layoutQuantity || this.formData.layoutQuantity < 1) {
            this.errors.layoutQuantity = '版式数量必须大于0';
            isValid = false;
          }

          return isValid;
        },
        calculatePrice () {
          if (!this.validateForm()) {
            return;
          }

          const config = this.currentConfig;
          const baseValue = this.formData.baseValue;
          const layoutPrice = this.formData.layoutPrice;
          const layoutQuantity = this.formData.layoutQuantity;

          const multiplier = this.getMultiplier(baseValue);
          const rawCostPrice = baseValue * multiplier;
          const costPrice = Math.max(rawCostPrice, config.minCostPrice);
          const layoutFee = layoutPrice * layoutQuantity;
          const finalPrice = costPrice + layoutFee;

          this.result = {
            show: true,
            finalPrice,
            costPrice,
            rawCostPrice,
            layoutFee,
            multiplier
          };

          // 添加到历史记录
          this.calculationHistory.push({
            timestamp: new Date(),
            categoryKey: this.currentCategory,
            categoryName: config.name,
            baseValue,
            layoutPrice,
            layoutQuantity,
            finalPrice,
            multiplier
          });

          // 保存到本地存储
          this.saveToLocalStorage();
        },
        autoCalculate () {
          this.errors = {};

          if (this.formData.baseValue &&
            this.formData.layoutPrice &&
            this.formData.layoutQuantity >= 1) {
            this.calculatePrice();
          }
        },
        getCategoryUsageCount (categoryKey) {
          return this.calculationHistory.filter(item => item.categoryKey === categoryKey).length;
        },
        loadHistoryItem (item) {
          this.currentCategory = item.categoryKey;
          this.formData = {
            baseValue: item.baseValue,
            layoutPrice: item.layoutPrice,
            layoutQuantity: item.layoutQuantity
          };
          this.autoCalculate();
        },
        formatTime (timestamp) {
          return new Date(timestamp).toLocaleString('zh-CN', {
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          });
        },
        toggleAdmin () {
          this.adminMode = !this.adminMode;
        },
        addCategory () {
          if (!this.newCategoryName.trim()) {
            alert('请输入品类名称');
            return;
          }

          const newKey = this.newCategoryName.toLowerCase().replace(/\s+/g, '');
          if (this.categoryConfigs[newKey]) {
            alert('该品类已存在');
            return;
          }

          // 复制售后卡的配置作为模板
          this.categoryConfigs[newKey] = {
            ...JSON.parse(JSON.stringify(this.categoryConfigs.afterSale)),
            name: this.newCategoryName.trim()
          };

          this.newCategoryName = '';
          alert('品类添加成功！');
        },
        editCategory () {
          if (!this.selectedCategoryToEdit) return;

          const config = this.categoryConfigs[this.selectedCategoryToEdit];
          const newName = prompt('输入新的品类名称:', config.name);
          if (newName && newName.trim()) {
            config.name = newName.trim();
            alert('品类名称已更新！');
          }
        },
        saveToLocalStorage () {
          try {
            localStorage.setItem('calculatorHistory', JSON.stringify(this.calculationHistory));
            localStorage.setItem('categoryConfigs', JSON.stringify(this.categoryConfigs));
          } catch (e) {
            console.warn('无法保存到本地存储');
          }
        },
        loadFromLocalStorage () {
          try {
            const history = localStorage.getItem('calculatorHistory');
            if (history) {
              this.calculationHistory = JSON.parse(history);
            }

            const configs = localStorage.getItem('categoryConfigs');
            if (configs) {
              this.categoryConfigs = { ...this.categoryConfigs, ...JSON.parse(configs) };
            }
          } catch (e) {
            console.warn('无法从本地存储加载数据');
          }
        }
      },
      mounted () {
        this.loadFromLocalStorage();
      }
    }).mount('#app');
  </script>
</body>

</html>