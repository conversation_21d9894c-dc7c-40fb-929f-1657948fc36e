# 多品类价格计算器

## 📋 项目概述

这是一个支持多品类的价格计算器，提供了三个不同的实现版本，支持以下4个品类：

1. **售后卡** - 主要品类，完整配置
2. **PVC卡亚克力** - 独立配置（高级版本中已差异化）
3. **名片代金券单页工艺纸** - 独立配置（高级版本中已差异化）
4. **人像证** - 独立配置（高级版本中已差异化）

## 🚀 版本对比

### 1. 原生JavaScript版本 (`index.html`)
- ✅ 无外部依赖，加载速度快
- ✅ 浏览器兼容性好
- ❌ 代码冗长，维护困难
- ❌ DOM操作繁琐

### 2. Vue.js基础版本 (`index-vue.html`)
- ✅ 响应式数据绑定
- ✅ 代码简洁易读
- ✅ 易于维护扩展
- ❌ 需要Vue.js依赖

### 3. Vue.js高级版本 (`index-vue-advanced.html`) ⭐ 推荐
- ✅ 完整的动态渲染能力
- ✅ 计算历史记录
- ✅ 统计信息展示
- ✅ 管理员模式（动态添加/编辑品类）
- ✅ 本地存储功能
- ✅ 不同品类的差异化配置

## 价格计算逻辑

### 成本价计算 (A)
- 原始成本价 × 对应的系数 = 计算后成本价
- 如果计算后成本价低于最小值，则使用最小值
- 目前所有品类的最小值都是 ¥15

### 版式费计算 (B)
- 版式单价 × 版式数量 = 版式费
- 版式费有最小值限制，目前所有品类的最小值都是 ¥8

### 最终价格
最终价格 = 成本价 + 版式费

## 系数配置

目前所有品类使用相同的系数配置：

| 原始成本价范围 | 系数 |
|---------------|------|
| 0 - 10.1 | 2.2 |
| 10.1 - 20 | 1.8 |
| 20 - 30 | 1.7 |
| 30 - 40 | 1.6 |
| 40 - 120 | 1.55 |
| 120 - 200 | 1.5 |
| 200 - 400 | 1.45 |
| 400 - 700 | 1.4 |
| 700 - 1000 | 1.35 |
| 1000 - 2000 | 1.3 |
| 2000 - 10000 | 1.25 |
| 10000+ | 1.2 |

## 版式规格选项

目前所有品类使用相同的版式规格：

- 90-54以下 - ¥8
- 90-54以上140-100以下 - ¥13
- 140-100以上285-210以下 - ¥20
- 285-210以上420-285以下 - ¥25
- 420-285以上570-420以下 - ¥35
- 570-420以上 - ¥50

## 使用方法

1. 在顶部选择对应的品类Tab
2. 输入原始成本价
3. 选择版式规格
4. 输入版式数量
5. 点击"计算价格"按钮或等待自动计算

## 技术特性

- 响应式设计，支持移动端
- 实时计算功能
- 美观的动画效果
- 详细的计算明细显示
- 输入验证和错误提示

## 📁 文件说明

- `index.html` - 原生JavaScript版本
- `index-vue.html` - Vue.js基础版本
- `index-vue-advanced.html` - Vue.js高级版本（推荐）
- `test.html` - 原生版本测试页面
- `comparison.html` - 版本对比页面
- `README.md` - 项目文档

## 🎯 Vue.js动态渲染的优势

### 1. 响应式数据绑定
```javascript
// 数据变化自动更新UI
v-model="formData.baseValue"
```

### 2. 计算属性
```javascript
computed: {
  currentConfig() {
    return this.categoryConfigs[this.currentCategory];
  }
}
```

### 3. 动态列表渲染
```html
<option v-for="option in currentConfig.layoutOptions" :key="option.value">
  {{ option.label }}
</option>
```

### 4. 条件渲染
```html
<div v-if="result.show" class="result">
  <!-- 计算结果 -->
</div>
```

## 🔧 高级版本特色功能

### 管理员模式
- 动态添加新品类
- 编辑现有品类名称
- 实时配置更新

### 统计功能
- 计算历史记录
- 使用统计分析
- 平均价格计算
- 热门品类识别

### 数据持久化
- 本地存储计算历史
- 自动保存品类配置
- 页面刷新数据不丢失

### 用户体验优化
- Tab使用次数徽章
- 点击历史记录快速填充
- 实时统计信息展示
- 响应式侧边栏布局

## 🚀 推荐使用方案

**强烈推荐使用Vue.js高级版本** (`index-vue-advanced.html`)，原因：

1. **动态渲染能力强** - 可以根据配置动态生成表单选项
2. **易于扩展** - 添加新品类只需修改配置对象
3. **用户体验好** - 历史记录、统计信息、管理功能
4. **维护成本低** - 代码结构清晰，逻辑集中
5. **功能完整** - 包含所有必要的业务功能

## 📊 品类配置差异化

高级版本中，不同品类已经实现了差异化配置：

### 售后卡
- 系数范围：1.2-2.2
- 最低成本价：¥15
- 版式费：¥8-50

### PVC卡亚克力
- 系数范围：1.1-2.0
- 最低成本价：¥20
- 版式费：¥10-40

### 名片代金券单页工艺纸
- 系数范围：1.0-1.8
- 最低成本价：¥5
- 版式费：¥3-12

### 人像证
- 系数范围：1.0-2.5
- 最低成本价：¥25
- 版式费：¥15-50

## 🔄 后续调整

如需调整品类配置，只需修改Vue应用中的 `categoryConfigs` 对象，所有相关UI会自动更新，无需手动修改HTML结构。
