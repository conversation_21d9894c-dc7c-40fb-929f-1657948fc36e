<!doctype html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>价格计算器</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      background-attachment: fixed;
      min-height: 100vh;
      padding: 20px;
      position: relative;
    }

    body::before {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.08)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
      pointer-events: none;
      z-index: 0;
    }

    .container {
      max-width: 900px;
      margin: 0 auto;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: 20px;
      box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.1);
      overflow: hidden;
      position: relative;
      z-index: 1;
    }

    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 40px 30px 20px;
      text-align: center;
      position: relative;
      overflow: hidden;
    }

    .header::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
      animation: float 6s ease-in-out infinite;
    }

    @keyframes float {

      0%,
      100% {
        transform: translate(-50%, -50%) rotate(0deg);
      }

      50% {
        transform: translate(-50%, -50%) rotate(180deg);
      }
    }

    .header h1 {
      font-size: 3em;
      margin-bottom: 15px;
      font-weight: 200;
      letter-spacing: 2px;
      position: relative;
      z-index: 2;
    }

    .header p {
      font-size: 1.2em;
      opacity: 0.9;
      position: relative;
      z-index: 2;
      font-weight: 300;
    }

    /* Tab 样式 */
    .tabs {
      display: flex;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 15px;
      padding: 5px;
      margin-top: 20px;
      position: relative;
      z-index: 2;
      overflow-x: auto;
    }

    .tab {
      flex: 1;
      padding: 12px 16px;
      text-align: center;
      background: transparent;
      border: none;
      color: rgba(255, 255, 255, 0.7);
      cursor: pointer;
      border-radius: 10px;
      transition: all 0.3s ease;
      font-size: 0.9em;
      font-weight: 500;
      white-space: nowrap;
      min-width: 120px;
    }

    .tab:hover {
      color: rgba(255, 255, 255, 0.9);
      background: rgba(255, 255, 255, 0.1);
    }

    .tab.active {
      background: rgba(255, 255, 255, 0.2);
      color: white;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .content {
      padding: 40px;
    }

    /* Tab 内容样式 */
    .tab-content {
      display: none;
    }

    .tab-content.active {
      display: block;
      animation: fadeIn 0.3s ease;
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(10px);
      }

      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .form-group {
      margin-bottom: 25px;
    }

    .form-group label {
      display: block;
      margin-bottom: 8px;
      font-weight: 600;
      color: #333;
      font-size: 1.1em;
    }

    .form-group input,
    .form-group select {
      width: 100%;
      padding: 15px 20px;
      border: 2px solid rgba(102, 126, 234, 0.1);
      border-radius: 12px;
      font-size: 1.1em;
      background: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(10px);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .form-group input:focus,
    .form-group select:focus {
      outline: none;
      border-color: #667eea;
      background: rgba(255, 255, 255, 1);
      box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1), 0 8px 25px rgba(102, 126, 234, 0.15);
      transform: translateY(-2px);
    }

    .calculate-btn {
      width: 100%;
      padding: 18px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      border-radius: 12px;
      font-size: 1.3em;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      margin-top: 30px;
      position: relative;
      overflow: hidden;
    }

    .calculate-btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s;
    }

    .calculate-btn:hover::before {
      left: 100%;
    }

    .calculate-btn:hover {
      transform: translateY(-3px);
      box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
    }

    .result {
      margin-top: 40px;
      display: none;
    }

    .result.show {
      display: block;
      animation: slideIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    }

    @keyframes slideIn {
      from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
      }

      to {
        opacity: 1;
        transform: translateY(0) scale(1);
      }
    }

    .final-price-card {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 20px;
      padding: 40px;
      text-align: center;
      margin-bottom: 30px;
      position: relative;
      overflow: hidden;
      box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
    }

    .final-price-card::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
      animation: rotate 8s linear infinite;
    }

    @keyframes rotate {
      from {
        transform: rotate(0deg);
      }

      to {
        transform: rotate(360deg);
      }
    }

    .final-price {
      font-size: 4em;
      font-weight: 300;
      color: white;
      margin-bottom: 10px;
      position: relative;
      z-index: 2;
      text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    }

    .price-label {
      font-size: 1.2em;
      color: rgba(255, 255, 255, 0.9);
      font-weight: 300;
      position: relative;
      z-index: 2;
    }

    .calculation-summary {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: 16px;
      padding: 25px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .calculation-summary h3 {
      color: #333;
      margin-bottom: 20px;
      font-size: 1.3em;
      font-weight: 500;
      text-align: center;
    }

    .summary-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid rgba(102, 126, 234, 0.1);
      font-size: 1.1em;
    }

    .summary-item:last-child {
      border-bottom: 2px solid #667eea;
      font-weight: 600;
      color: #667eea;
      margin-top: 10px;
      padding-top: 20px;
    }

    .summary-label {
      color: #666;
    }

    .summary-value {
      font-weight: 500;
      color: #333;
    }

    .summary-item:last-child .summary-label,
    .summary-item:last-child .summary-value {
      color: #667eea;
    }

    .error {
      color: #e74c3c;
      font-size: 0.9em;
      margin-top: 5px;
    }

    .input-group {
      display: flex;
      gap: 15px;
    }

    .input-group .form-group {
      flex: 1;
    }

    @media (max-width: 600px) {
      .input-group {
        flex-direction: column;
      }

      .content {
        padding: 20px;
      }

      .header {
        padding: 20px;
      }

      .header h1 {
        font-size: 2em;
      }
    }
  </style>
</head>

<body>
  <div class="container">
    <div class="header">
      <h1>价格计算器</h1>
      <div class="tabs">
        <button class="tab active" data-tab="afterSale">售后卡</button>
        <button class="tab" data-tab="pvcAcrylic">PVC卡亚克力</button>
        <button class="tab" data-tab="businessCard">名片代金券单页工艺纸</button>
        <button class="tab" data-tab="portrait">人像证</button>
      </div>
    </div>

    <div class="content">
      <!-- 售后卡 Tab -->
      <div id="afterSale" class="tab-content active">
        <form class="price-form">
          <div class="form-group">
            <label for="afterSale-baseValue">原始成本价:</label>
            <input type="number" id="afterSale-baseValue" min="0.01" step="0.01" required placeholder="请输入原始成本价">
            <div class="error" id="afterSale-baseValueError"></div>
          </div>

          <div class="input-group">
            <div class="form-group">
              <label for="afterSale-layoutPrice">版式单价:</label>
              <select id="afterSale-layoutPrice" required>
                <option value="">请选择版式规格</option>
                <option value="8">90-54以下 - ¥8</option>
                <option value="13">90-54以上140-100以下 - ¥13</option>
                <option value="20">140-100以上285-210以下 - ¥20</option>
                <option value="25">285-210以上420-285以下 - ¥25</option>
                <option value="35">420-285以上570-420以下 - ¥35</option>
                <option value="50">570-420以上 - ¥50</option>
              </select>
            </div>

            <div class="form-group">
              <label for="afterSale-layoutQuantity">版式数量:</label>
              <input type="number" id="afterSale-layoutQuantity" step="1" min="1" required placeholder="版式数量">
              <div class="error" id="afterSale-layoutQuantityError"></div>
            </div>
          </div>

          <button type="submit" class="calculate-btn">计算价格</button>
        </form>
      </div>

      <!-- PVC卡亚克力 Tab -->
      <div id="pvcAcrylic" class="tab-content">
        <form class="price-form">
          <div class="form-group">
            <label for="pvcAcrylic-baseValue">原始成本价:</label>
            <input type="number" id="pvcAcrylic-baseValue" min="0.01" step="0.01" required placeholder="请输入原始成本价">
            <div class="error" id="pvcAcrylic-baseValueError"></div>
          </div>

          <div class="input-group">
            <div class="form-group">
              <label for="pvcAcrylic-layoutPrice">版式单价:</label>
              <select id="pvcAcrylic-layoutPrice" required>
                <option value="">请选择版式规格</option>
                <option value="8">90-54以下 - ¥8</option>
                <option value="13">90-54以上140-100以下 - ¥13</option>
                <option value="20">140-100以上285-210以下 - ¥20</option>
                <option value="25">285-210以上420-285以下 - ¥25</option>
                <option value="35">420-285以上570-420以下 - ¥35</option>
                <option value="50">570-420以上 - ¥50</option>
              </select>
            </div>

            <div class="form-group">
              <label for="pvcAcrylic-layoutQuantity">版式数量:</label>
              <input type="number" id="pvcAcrylic-layoutQuantity" step="1" min="1" required placeholder="版式数量">
              <div class="error" id="pvcAcrylic-layoutQuantityError"></div>
            </div>
          </div>

          <button type="submit" class="calculate-btn">计算价格</button>
        </form>
      </div>

      <!-- 名片代金券单页工艺纸 Tab -->
      <div id="businessCard" class="tab-content">
        <form class="price-form">
          <div class="form-group">
            <label for="businessCard-baseValue">原始成本价:</label>
            <input type="number" id="businessCard-baseValue" min="0.01" step="0.01" required placeholder="请输入原始成本价">
            <div class="error" id="businessCard-baseValueError"></div>
          </div>

          <div class="input-group">
            <div class="form-group">
              <label for="businessCard-layoutPrice">版式单价:</label>
              <select id="businessCard-layoutPrice" required>
                <option value="">请选择版式规格</option>
                <option value="8">90-54以下 - ¥8</option>
                <option value="13">90-54以上140-100以下 - ¥13</option>
                <option value="20">140-100以上285-210以下 - ¥20</option>
                <option value="25">285-210以上420-285以下 - ¥25</option>
                <option value="35">420-285以上570-420以下 - ¥35</option>
                <option value="50">570-420以上 - ¥50</option>
              </select>
            </div>

            <div class="form-group">
              <label for="businessCard-layoutQuantity">版式数量:</label>
              <input type="number" id="businessCard-layoutQuantity" step="1" min="1" required placeholder="版式数量">
              <div class="error" id="businessCard-layoutQuantityError"></div>
            </div>
          </div>

          <button type="submit" class="calculate-btn">计算价格</button>
        </form>
      </div>

      <!-- 人像证 Tab -->
      <div id="portrait" class="tab-content">
        <form class="price-form">
          <div class="form-group">
            <label for="portrait-baseValue">原始成本价:</label>
            <input type="number" id="portrait-baseValue" min="0.01" step="0.01" required placeholder="请输入原始成本价">
            <div class="error" id="portrait-baseValueError"></div>
          </div>

          <div class="input-group">
            <div class="form-group">
              <label for="portrait-layoutPrice">版式单价:</label>
              <select id="portrait-layoutPrice" required>
                <option value="">请选择版式规格</option>
                <option value="8">90-54以下 - ¥8</option>
                <option value="13">90-54以上140-100以下 - ¥13</option>
                <option value="20">140-100以上285-210以下 - ¥20</option>
                <option value="25">285-210以上420-285以下 - ¥25</option>
                <option value="35">420-285以上570-420以下 - ¥35</option>
                <option value="50">570-420以上 - ¥50</option>
              </select>
            </div>

            <div class="form-group">
              <label for="portrait-layoutQuantity">版式数量:</label>
              <input type="number" id="portrait-layoutQuantity" step="1" min="1" required placeholder="版式数量">
              <div class="error" id="portrait-layoutQuantityError"></div>
            </div>
          </div>

          <button type="submit" class="calculate-btn">计算价格</button>
        </form>
      </div>

      <div class="result" id="result">
        <div class="final-price-card">
          <div class="final-price" id="finalPrice">¥0.00</div>
          <div class="price-label">最终价格</div>
        </div>

        <div class="calculation-summary">
          <h3>💡 计算明细</h3>
          <div id="calculationSummary">
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // 品类配置
    const categoryConfigs = {
      afterSale: {
        name: '售后卡',
        multiplierRanges: [
          { min: 0, max: 10.1, multiplier: 2.2 },
          { min: 10.1, max: 20, multiplier: 1.8 },
          { min: 20, max: 30, multiplier: 1.7 },
          { min: 30, max: 40, multiplier: 1.6 },
          { min: 40, max: 120, multiplier: 1.55 },
          { min: 120, max: 200, multiplier: 1.5 },
          { min: 200, max: 400, multiplier: 1.45 },
          { min: 400, max: 700, multiplier: 1.4 },
          { min: 700, max: 1000, multiplier: 1.35 },
          { min: 1000, max: 2000, multiplier: 1.3 },
          { min: 2000, max: 10000, multiplier: 1.25 },
          { min: 10000, max: Infinity, multiplier: 1.2 }
        ],
        minCostPrice: 15,
        layoutOptions: [
          { value: 8, label: '90-54以下 - ¥8' },
          { value: 13, label: '90-54以上140-100以下 - ¥13' },
          { value: 20, label: '140-100以上285-210以下 - ¥20' },
          { value: 25, label: '285-210以上420-285以下 - ¥25' },
          { value: 35, label: '420-285以上570-420以下 - ¥35' },
          { value: 50, label: '570-420以上 - ¥50' }
        ],
        minLayoutFee: 8
      },
      pvcAcrylic: {
        name: 'PVC卡亚克力',
        multiplierRanges: [
          { min: 0, max: 10.1, multiplier: 2.2 },
          { min: 10.1, max: 20, multiplier: 1.8 },
          { min: 20, max: 30, multiplier: 1.7 },
          { min: 30, max: 40, multiplier: 1.6 },
          { min: 40, max: 120, multiplier: 1.55 },
          { min: 120, max: 200, multiplier: 1.5 },
          { min: 200, max: 400, multiplier: 1.45 },
          { min: 400, max: 700, multiplier: 1.4 },
          { min: 700, max: 1000, multiplier: 1.35 },
          { min: 1000, max: 2000, multiplier: 1.3 },
          { min: 2000, max: 10000, multiplier: 1.25 },
          { min: 10000, max: Infinity, multiplier: 1.2 }
        ],
        minCostPrice: 15,
        layoutOptions: [
          { value: 8, label: '90-54以下 - ¥8' },
          { value: 13, label: '90-54以上140-100以下 - ¥13' },
          { value: 20, label: '140-100以上285-210以下 - ¥20' },
          { value: 25, label: '285-210以上420-285以下 - ¥25' },
          { value: 35, label: '420-285以上570-420以下 - ¥35' },
          { value: 50, label: '570-420以上 - ¥50' }
        ],
        minLayoutFee: 8
      },
      businessCard: {
        name: '名片代金券单页工艺纸',
        multiplierRanges: [
          { min: 0, max: 10.1, multiplier: 2.2 },
          { min: 10.1, max: 20, multiplier: 1.8 },
          { min: 20, max: 30, multiplier: 1.7 },
          { min: 30, max: 40, multiplier: 1.6 },
          { min: 40, max: 120, multiplier: 1.55 },
          { min: 120, max: 200, multiplier: 1.5 },
          { min: 200, max: 400, multiplier: 1.45 },
          { min: 400, max: 700, multiplier: 1.4 },
          { min: 700, max: 1000, multiplier: 1.35 },
          { min: 1000, max: 2000, multiplier: 1.3 },
          { min: 2000, max: 10000, multiplier: 1.25 },
          { min: 10000, max: Infinity, multiplier: 1.2 }
        ],
        minCostPrice: 15,
        layoutOptions: [
          { value: 8, label: '90-54以下 - ¥8' },
          { value: 13, label: '90-54以上140-100以下 - ¥13' },
          { value: 20, label: '140-100以上285-210以下 - ¥20' },
          { value: 25, label: '285-210以上420-285以下 - ¥25' },
          { value: 35, label: '420-285以上570-420以下 - ¥35' },
          { value: 50, label: '570-420以上 - ¥50' }
        ],
        minLayoutFee: 8
      },
      portrait: {
        name: '人像证',
        multiplierRanges: [
          { min: 0, max: 10.1, multiplier: 2.2 },
          { min: 10.1, max: 20, multiplier: 1.8 },
          { min: 20, max: 30, multiplier: 1.7 },
          { min: 30, max: 40, multiplier: 1.6 },
          { min: 40, max: 120, multiplier: 1.55 },
          { min: 120, max: 200, multiplier: 1.5 },
          { min: 200, max: 400, multiplier: 1.45 },
          { min: 400, max: 700, multiplier: 1.4 },
          { min: 700, max: 1000, multiplier: 1.35 },
          { min: 1000, max: 2000, multiplier: 1.3 },
          { min: 2000, max: 10000, multiplier: 1.25 },
          { min: 10000, max: Infinity, multiplier: 1.2 }
        ],
        minCostPrice: 15,
        layoutOptions: [
          { value: 8, label: '90-54以下 - ¥8' },
          { value: 13, label: '90-54以上140-100以下 - ¥13' },
          { value: 20, label: '140-100以上285-210以下 - ¥20' },
          { value: 25, label: '285-210以上420-285以下 - ¥25' },
          { value: 35, label: '420-285以上570-420以下 - ¥35' },
          { value: 50, label: '570-420以上 - ¥50' }
        ],
        minLayoutFee: 8
      }
    };

    let currentCategory = 'afterSale';

    function getMultiplier (value, category) {
      const config = categoryConfigs[category];
      for (let range of config.multiplierRanges) {
        if (value >= range.min && value < range.max) {
          return range.multiplier;
        }
      }
      return 1.2; // 默认倍率
    }

    // Tab 切换功能
    function switchTab (tabId) {
      // 隐藏所有 tab 内容
      document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
      });

      // 移除所有 tab 的 active 状态
      document.querySelectorAll('.tab').forEach(tab => {
        tab.classList.remove('active');
      });

      // 显示选中的 tab 内容
      document.getElementById(tabId).classList.add('active');

      // 设置选中的 tab 为 active
      document.querySelector(`[data-tab="${tabId}"]`).classList.add('active');

      // 更新当前品类
      currentCategory = tabId;

      // 隐藏结果
      document.getElementById('result').classList.remove('show');
    }

    function calculatePrice (category) {
      const config = categoryConfigs[category];
      const baseValue = parseFloat(document.getElementById(`${category}-baseValue`).value);
      const layoutPrice = parseFloat(document.getElementById(`${category}-layoutPrice`).value);
      const layoutQuantity = parseInt(document.getElementById(`${category}-layoutQuantity`).value);

      // 清除之前的错误信息
      document.getElementById(`${category}-baseValueError`).textContent = '';
      document.getElementById(`${category}-layoutQuantityError`).textContent = '';

      // 验证输入
      if (!baseValue || baseValue <= 0) {
        document.getElementById(`${category}-baseValueError`).textContent = '请输入有效的正数';
        return;
      }

      if (!layoutPrice) {
        alert('请选择版式规格');
        return;
      }

      if (!layoutQuantity) {
        document.getElementById(`${category}-layoutQuantityError`).textContent = '版式数量必填';
        return;
      }

      // 计算成本价
      const multiplier = getMultiplier(baseValue, category);
      const rawCostPrice = baseValue * multiplier;
      const costPrice = Math.max(rawCostPrice, config.minCostPrice);

      // 计算版式费
      const layoutFee = layoutPrice * layoutQuantity;

      // 计算最终价格
      const finalPrice = costPrice + layoutFee;

      // 显示计算摘要
      const calculationSummary = document.getElementById('calculationSummary');
      calculationSummary.innerHTML = `
        <div class="summary-item">
          <span class="summary-label">品类</span>
          <span class="summary-value">${config.name}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">原始成本价</span>
          <span class="summary-value">¥${baseValue}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">倍率</span>
          <span class="summary-value">×${multiplier}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">实际成本价</span>
          <span class="summary-value">¥${costPrice.toFixed(2)} ${rawCostPrice < config.minCostPrice ? `(最低¥${config.minCostPrice})` : ''}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">版式费</span>
          <span class="summary-value">¥${layoutPrice} × ${layoutQuantity} = ¥${layoutFee.toFixed(2)}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">最终价格</span>
          <span class="summary-value">¥${finalPrice.toFixed(2)}</span>
        </div>
      `;

      document.getElementById('finalPrice').textContent = `¥${finalPrice.toFixed(2)}`;
      document.getElementById('result').classList.add('show');
    }

    // 初始化事件监听器
    document.addEventListener('DOMContentLoaded', function () {
      // Tab 切换事件
      document.querySelectorAll('.tab').forEach(tab => {
        tab.addEventListener('click', function () {
          const tabId = this.getAttribute('data-tab');
          switchTab(tabId);
        });
      });

      // 表单提交事件
      document.querySelectorAll('.price-form').forEach(form => {
        form.addEventListener('submit', function (e) {
          e.preventDefault();
          const tabContent = this.closest('.tab-content');
          const category = tabContent.id;
          calculatePrice(category);
        });
      });

      // 为每个品类添加实时计算事件
      Object.keys(categoryConfigs).forEach(category => {
        const baseValueInput = document.getElementById(`${category}-baseValue`);
        const layoutPriceSelect = document.getElementById(`${category}-layoutPrice`);
        const layoutQuantityInput = document.getElementById(`${category}-layoutQuantity`);

        baseValueInput.addEventListener('input', function () {
          if (this.value && layoutPriceSelect.value && layoutQuantityInput.value >= 1) {
            calculatePrice(category);
          }
        });

        layoutPriceSelect.addEventListener('change', function () {
          if (baseValueInput.value && this.value && layoutQuantityInput.value >= 1) {
            calculatePrice(category);
          }
        });

        layoutQuantityInput.addEventListener('input', function () {
          // 清除错误信息
          document.getElementById(`${category}-layoutQuantityError`).textContent = '';

          if (baseValueInput.value && layoutPriceSelect.value && this.value >= 1) {
            calculatePrice(category);
          }
        });
      });
    });
  </script>
</body>

</html>