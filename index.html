<!doctype html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>价格计算器</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      background-attachment: fixed;
      min-height: 100vh;
      padding: 20px;
      position: relative;
    }

    body::before {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.08)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
      pointer-events: none;
      z-index: 0;
    }

    .container {
      max-width: 900px;
      margin: 0 auto;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: 20px;
      box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.1);
      overflow: hidden;
      position: relative;
      z-index: 1;
    }

    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 40px 30px;
      text-align: center;
      position: relative;
      overflow: hidden;
    }

    .header::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
      animation: float 6s ease-in-out infinite;
    }

    @keyframes float {

      0%,
      100% {
        transform: translate(-50%, -50%) rotate(0deg);
      }

      50% {
        transform: translate(-50%, -50%) rotate(180deg);
      }
    }

    .header h1 {
      font-size: 3em;
      margin-bottom: 15px;
      font-weight: 200;
      letter-spacing: 2px;
      position: relative;
      z-index: 2;
    }

    .header p {
      font-size: 1.2em;
      opacity: 0.9;
      position: relative;
      z-index: 2;
      font-weight: 300;
    }

    .content {
      padding: 40px;
    }

    .form-group {
      margin-bottom: 25px;
    }

    .form-group label {
      display: block;
      margin-bottom: 8px;
      font-weight: 600;
      color: #333;
      font-size: 1.1em;
    }

    .form-group input,
    .form-group select {
      width: 100%;
      padding: 15px 20px;
      border: 2px solid rgba(102, 126, 234, 0.1);
      border-radius: 12px;
      font-size: 1.1em;
      background: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(10px);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .form-group input:focus,
    .form-group select:focus {
      outline: none;
      border-color: #667eea;
      background: rgba(255, 255, 255, 1);
      box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1), 0 8px 25px rgba(102, 126, 234, 0.15);
      transform: translateY(-2px);
    }

    .calculate-btn {
      width: 100%;
      padding: 18px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      border-radius: 12px;
      font-size: 1.3em;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      margin-top: 30px;
      position: relative;
      overflow: hidden;
    }

    .calculate-btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s;
    }

    .calculate-btn:hover::before {
      left: 100%;
    }

    .calculate-btn:hover {
      transform: translateY(-3px);
      box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
    }

    .result {
      margin-top: 40px;
      display: none;
    }

    .result.show {
      display: block;
      animation: slideIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    }

    @keyframes slideIn {
      from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
      }

      to {
        opacity: 1;
        transform: translateY(0) scale(1);
      }
    }

    .final-price-card {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 20px;
      padding: 40px;
      text-align: center;
      margin-bottom: 30px;
      position: relative;
      overflow: hidden;
      box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
    }

    .final-price-card::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
      animation: rotate 8s linear infinite;
    }

    @keyframes rotate {
      from {
        transform: rotate(0deg);
      }

      to {
        transform: rotate(360deg);
      }
    }

    .final-price {
      font-size: 4em;
      font-weight: 300;
      color: white;
      margin-bottom: 10px;
      position: relative;
      z-index: 2;
      text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    }

    .price-label {
      font-size: 1.2em;
      color: rgba(255, 255, 255, 0.9);
      font-weight: 300;
      position: relative;
      z-index: 2;
    }

    .calculation-summary {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: 16px;
      padding: 25px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .calculation-summary h3 {
      color: #333;
      margin-bottom: 20px;
      font-size: 1.3em;
      font-weight: 500;
      text-align: center;
    }

    .summary-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid rgba(102, 126, 234, 0.1);
      font-size: 1.1em;
    }

    .summary-item:last-child {
      border-bottom: 2px solid #667eea;
      font-weight: 600;
      color: #667eea;
      margin-top: 10px;
      padding-top: 20px;
    }

    .summary-label {
      color: #666;
    }

    .summary-value {
      font-weight: 500;
      color: #333;
    }

    .summary-item:last-child .summary-label,
    .summary-item:last-child .summary-value {
      color: #667eea;
    }

    .error {
      color: #e74c3c;
      font-size: 0.9em;
      margin-top: 5px;
    }

    .input-group {
      display: flex;
      gap: 15px;
    }

    .input-group .form-group {
      flex: 1;
    }

    @media (max-width: 600px) {
      .input-group {
        flex-direction: column;
      }

      .content {
        padding: 20px;
      }

      .header {
        padding: 20px;
      }

      .header h1 {
        font-size: 2em;
      }
    }
  </style>
</head>

<body>
  <div class="container">
    <div class="header">
      <h1>价格计算器</h1>
    </div>

    <div class="content">
      <form id="priceForm">
        <div class="form-group">
          <label for="baseValue">原始成本价:</label>
          <input type="number" id="baseValue" min="0.01" step="0.01" required placeholder="请输入原始成本价">
          <div class="error" id="baseValueError"></div>
        </div>

        <div class="input-group">
          <div class="form-group">
            <label for="layoutPrice">版式单价:</label>
            <select id="layoutPrice" required>
              <option value="">请选择版式规格</option>
              <option value="8">90-54以下 - ¥8</option>
              <option value="13">90-54以上140-100以下 - ¥13</option>
              <option value="20">140-100以上285-210以下 - ¥20</option>
              <option value="25">285-210以上420-285以下 - ¥25</option>
              <option value="35">420-285以上570-420以下 - ¥35</option>
              <option value="50">570-420以上 - ¥50</option>
            </select>
          </div>

          <div class="form-group">
            <label for="layoutQuantity">版式数量:</label>
            <input type="number" id="layoutQuantity" step="1" min="1" required placeholder="版式数量">
            <div class="error" id="layoutQuantityError"></div>
          </div>
        </div>

        <button type="submit" class="calculate-btn">计算价格</button>
      </form>

      <div class="result" id="result">
        <div class="final-price-card">
          <div class="final-price" id="finalPrice">¥0.00</div>
          <div class="price-label">最终价格</div>
        </div>

        <div class="calculation-summary">
          <h3>💡 计算明细</h3>
          <div id="calculationSummary">
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // 倍率区间配置
    const multiplierRanges = [
      { min: 0, max: 10.1, multiplier: 2.2 },
      { min: 10.1, max: 20, multiplier: 1.8 },
      { min: 20, max: 30, multiplier: 1.7 },
      { min: 30, max: 40, multiplier: 1.6 },
      { min: 40, max: 120, multiplier: 1.55 },
      { min: 120, max: 200, multiplier: 1.5 },
      { min: 200, max: 400, multiplier: 1.45 },
      { min: 400, max: 700, multiplier: 1.4 },
      { min: 700, max: 1000, multiplier: 1.35 },
      { min: 1000, max: 2000, multiplier: 1.3 },
      { min: 2000, max: 10000, multiplier: 1.25 },
      { min: 10000, max: Infinity, multiplier: 1.2 }
    ];

    function getMultiplier (value) {
      for (let range of multiplierRanges) {
        if (value >= range.min && value < range.max) {
          return range.multiplier;
        }
      }
      return 1.2; // 默认倍率
    }

    function calculatePrice () {
      const baseValue = parseFloat(document.getElementById('baseValue').value);
      const layoutPrice = parseFloat(document.getElementById('layoutPrice').value);
      const layoutQuantity = parseInt(document.getElementById('layoutQuantity').value);

      // 清除之前的错误信息
      document.getElementById('baseValueError').textContent = '';
      document.getElementById('layoutQuantityError').textContent = '';

      // 验证输入
      if (!baseValue || baseValue <= 0) {
        document.getElementById('baseValueError').textContent = '请输入有效的正数';
        return;
      }

      if (!layoutPrice) {
        alert('请选择版式规格');
        return;
      }

      if (!layoutQuantity) {
        document.getElementById('layoutQuantityError').textContent = '版式数量必填';
        return;
      }

      // 计算成本价
      const multiplier = getMultiplier(baseValue);
      const rawCostPrice = baseValue * multiplier;
      const costPrice = Math.max(rawCostPrice, 15); // 最低15

      // 计算版式费
      const layoutFee = layoutPrice * layoutQuantity;

      // 计算最终价格
      const finalPrice = costPrice + layoutFee;

      // 显示计算摘要
      const calculationSummary = document.getElementById('calculationSummary');
      calculationSummary.innerHTML = `
        <div class="summary-item">
          <span class="summary-label">原始成本价</span>
          <span class="summary-value">¥${baseValue}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">倍率</span>
          <span class="summary-value">×${multiplier}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">实际成本价</span>
          <span class="summary-value">¥${costPrice.toFixed(2)} ${rawCostPrice < 15 ? '(最低¥15)' : ''}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">版式费</span>
          <span class="summary-value">¥${layoutPrice} × ${layoutQuantity} = ¥${layoutFee.toFixed(2)}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">最终价格</span>
          <span class="summary-value">¥${finalPrice.toFixed(2)}</span>
        </div>
      `;

      document.getElementById('finalPrice').textContent = `¥${finalPrice.toFixed(2)}`;
      document.getElementById('result').classList.add('show');
    }

    // 表单提交事件
    document.getElementById('priceForm').addEventListener('submit', function (e) {
      e.preventDefault();
      calculatePrice();
    });

    // 实时计算（可选）
    document.getElementById('baseValue').addEventListener('input', function () {
      if (this.value && document.getElementById('layoutPrice').value && document.getElementById('layoutQuantity').value >= 8) {
        calculatePrice();
      }
    });

    document.getElementById('layoutPrice').addEventListener('change', function () {
      if (document.getElementById('baseValue').value && this.value && document.getElementById('layoutQuantity').value >= 8) {
        calculatePrice();
      }
    });

    document.getElementById('layoutQuantity').addEventListener('input', function () {
      // 清除错误信息
      document.getElementById('layoutQuantityError').textContent = '';

      if (document.getElementById('baseValue').value && document.getElementById('layoutPrice').value && this.value >= 8) {
        calculatePrice();
      }
    });
  </script>
</body>

</html>