<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>价格计算器版本对比</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2.5em;
        }
        
        .header p {
            color: #666;
            font-size: 1.2em;
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .version-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .version-card:hover {
            transform: translateY(-5px);
        }
        
        .version-card h2 {
            color: #667eea;
            margin-bottom: 20px;
            font-size: 1.8em;
            text-align: center;
        }
        
        .features {
            margin-bottom: 25px;
        }
        
        .features h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.2em;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
            position: relative;
            padding-left: 25px;
        }
        
        .feature-list li:before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #4CAF50;
            font-weight: bold;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .pros-cons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 25px;
        }
        
        .pros, .cons {
            padding: 15px;
            border-radius: 8px;
        }
        
        .pros {
            background: #e8f5e8;
            border-left: 4px solid #4CAF50;
        }
        
        .cons {
            background: #ffeaa7;
            border-left: 4px solid #fdcb6e;
        }
        
        .pros h4, .cons h4 {
            margin-bottom: 10px;
            font-size: 1em;
        }
        
        .pros h4 {
            color: #2d5a2d;
        }
        
        .cons h4 {
            color: #8b6914;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
        }
        
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #dee2e6;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .summary {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .summary h2 {
            color: #333;
            margin-bottom: 20px;
        }
        
        .summary p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 15px;
        }
        
        .recommendation {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }
        
        @media (max-width: 768px) {
            .comparison-grid {
                grid-template-columns: 1fr;
            }
            
            .pros-cons {
                grid-template-columns: 1fr;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>价格计算器版本对比</h1>
        <p>原生JavaScript vs Vue.js 实现对比</p>
    </div>

    <div class="comparison-grid">
        <!-- 原生JavaScript版本 -->
        <div class="version-card">
            <h2>🚀 原生JavaScript版本</h2>
            
            <div class="features">
                <h3>核心功能</h3>
                <ul class="feature-list">
                    <li>多品类Tab切换</li>
                    <li>实时价格计算</li>
                    <li>输入验证</li>
                    <li>详细计算明细</li>
                    <li>响应式设计</li>
                    <li>美观动画效果</li>
                </ul>
            </div>

            <div class="pros-cons">
                <div class="pros">
                    <h4>✅ 优点</h4>
                    <ul>
                        <li>无外部依赖</li>
                        <li>加载速度快</li>
                        <li>文件体积小</li>
                        <li>浏览器兼容性好</li>
                    </ul>
                </div>
                <div class="cons">
                    <h4>⚠️ 缺点</h4>
                    <ul>
                        <li>代码冗长复杂</li>
                        <li>DOM操作繁琐</li>
                        <li>状态管理困难</li>
                        <li>扩展性较差</li>
                    </ul>
                </div>
            </div>

            <div class="action-buttons">
                <a href="index.html" target="_blank" class="btn btn-primary">打开原生版本</a>
                <a href="test.html" target="_blank" class="btn btn-secondary">查看测试</a>
            </div>
        </div>

        <!-- Vue.js版本 -->
        <div class="version-card">
            <h2>⚡ Vue.js版本</h2>
            
            <div class="features">
                <h3>核心功能</h3>
                <ul class="feature-list">
                    <li>响应式数据绑定</li>
                    <li>组件化架构</li>
                    <li>声明式渲染</li>
                    <li>计算属性</li>
                    <li>动态内容渲染</li>
                    <li>优雅的状态管理</li>
                </ul>
            </div>

            <div class="pros-cons">
                <div class="pros">
                    <h4>✅ 优点</h4>
                    <ul>
                        <li>代码简洁易读</li>
                        <li>响应式数据绑定</li>
                        <li>易于维护扩展</li>
                        <li>开发效率高</li>
                    </ul>
                </div>
                <div class="cons">
                    <h4>⚠️ 缺点</h4>
                    <ul>
                        <li>需要Vue.js依赖</li>
                        <li>学习成本</li>
                        <li>文件稍大</li>
                        <li>需要框架知识</li>
                    </ul>
                </div>
            </div>

            <div class="action-buttons">
                <a href="index-vue.html" target="_blank" class="btn btn-primary">打开Vue版本</a>
                <a href="#" onclick="createVueTest()" class="btn btn-secondary">动态测试</a>
            </div>
        </div>
    </div>

    <div class="summary">
        <h2>📊 总结与建议</h2>
        <p>两个版本都实现了完整的多品类价格计算功能，包括Tab切换、实时计算、输入验证等核心特性。</p>
        <p>原生JavaScript版本适合对性能要求极高、不希望引入外部依赖的场景。</p>
        <p>Vue.js版本代码更简洁、易维护，适合需要频繁修改和扩展功能的场景。</p>
        
        <div class="recommendation">
            <h3>🎯 推荐使用Vue.js版本</h3>
            <p>考虑到后续可能需要调整不同品类的配置、添加新功能等需求，Vue.js版本的动态渲染能力和响应式数据绑定将大大提高开发效率和代码可维护性。</p>
        </div>
    </div>

    <script>
        function createVueTest() {
            // 创建一个简单的Vue测试示例
            const testWindow = window.open('', '_blank', 'width=600,height=400');
            testWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Vue动态测试</title>
                    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
                    <style>
                        body { font-family: Arial, sans-serif; padding: 20px; }
                        .demo { background: #f0f0f0; padding: 15px; border-radius: 8px; margin: 10px 0; }
                        input, select { padding: 8px; margin: 5px; border: 1px solid #ccc; border-radius: 4px; }
                        button { padding: 10px 20px; background: #667eea; color: white; border: none; border-radius: 4px; cursor: pointer; }
                    </style>
                </head>
                <body>
                    <div id="app">
                        <h2>Vue.js 动态渲染演示</h2>
                        <div class="demo">
                            <h3>实时数据绑定</h3>
                            <input v-model="message" placeholder="输入任何内容">
                            <p>你输入的内容：{{ message }}</p>
                        </div>
                        
                        <div class="demo">
                            <h3>动态列表渲染</h3>
                            <button @click="addItem">添加品类</button>
                            <ul>
                                <li v-for="(item, index) in items" :key="index">
                                    {{ item }} 
                                    <button @click="removeItem(index)" style="margin-left: 10px; padding: 2px 8px; font-size: 12px;">删除</button>
                                </li>
                            </ul>
                        </div>
                        
                        <div class="demo">
                            <h3>计算属性演示</h3>
                            <input v-model.number="price" type="number" placeholder="输入价格">
                            <select v-model.number="multiplier">
                                <option value="1.2">1.2倍</option>
                                <option value="1.5">1.5倍</option>
                                <option value="2.0">2.0倍</option>
                            </select>
                            <p>计算结果：¥{{ calculatedPrice.toFixed(2) }}</p>
                        </div>
                    </div>
                    
                    <script>
                        const { createApp } = Vue;
                        createApp({
                            data() {
                                return {
                                    message: 'Hello Vue!',
                                    items: ['售后卡', 'PVC卡亚克力', '名片代金券'],
                                    price: 100,
                                    multiplier: 1.5
                                }
                            },
                            computed: {
                                calculatedPrice() {
                                    return (this.price || 0) * this.multiplier;
                                }
                            },
                            methods: {
                                addItem() {
                                    const newItem = prompt('输入新品类名称:');
                                    if (newItem) {
                                        this.items.push(newItem);
                                    }
                                },
                                removeItem(index) {
                                    this.items.splice(index, 1);
                                }
                            }
                        }).mount('#app');
                    </script>
                </body>
                </html>
            `);
        }
    </script>
</body>
</html>
